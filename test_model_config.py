#!/usr/bin/env python3
"""
测试模型配置脚本
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_model_config():
    """测试模型配置"""
    try:
        from model_config import get_model_config, create_model_kwargs, validate_model_environment
        
        model_name = os.getenv("MODEL", "deepseek-chat")
        print(f"🔍 测试模型: {model_name}")
        
        # 1. 获取模型配置
        try:
            config = get_model_config(model_name)
            print(f"✅ 模型配置获取成功:")
            print(f"   - 提供商: {config.provider}")
            print(f"   - 模型名称: {config.model_name}")
            print(f"   - API密钥环境变量: {config.api_key_env}")
            print(f"   - Base URL: {config.base_url}")
        except Exception as e:
            print(f"❌ 模型配置获取失败: {e}")
            return False
        
        # 2. 验证环境变量
        api_key = os.getenv(config.api_key_env)
        print(f"\n🔑 API密钥检查:")
        print(f"   - 环境变量: {config.api_key_env}")
        print(f"   - 是否设置: {'✅' if api_key else '❌'}")
        if api_key:
            print(f"   - 密钥长度: {len(api_key)} 字符")
        
        # 3. 验证模型环境
        is_valid = validate_model_environment(model_name)
        print(f"\n🌍 环境验证: {'✅ 通过' if is_valid else '❌ 失败'}")
        
        # 4. 创建模型参数
        if is_valid:
            try:
                kwargs = create_model_kwargs(model_name)
                print(f"\n⚙️ 模型参数:")
                for key, value in kwargs.items():
                    if key == "api_key":
                        print(f"   - {key}: {'***' + value[-4:] if value and len(value) > 4 else '***'}")
                    else:
                        print(f"   - {key}: {value}")
            except Exception as e:
                print(f"❌ 模型参数创建失败: {e}")
                return False
        
        return is_valid
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False


def test_langchain_init():
    """测试 LangChain init_chat_model"""
    try:
        from langchain.chat_models import init_chat_model
        from model_config import create_model_kwargs
        
        model_name = os.getenv("MODEL", "deepseek-chat")
        print(f"\n🚀 测试 LangChain 模型初始化: {model_name}")
        
        # 创建模型参数
        kwargs = create_model_kwargs(model_name)
        
        # 初始化模型
        model = init_chat_model(**kwargs)
        print(f"✅ 模型初始化成功: {type(model).__name__}")
        
        # 测试简单调用
        print("🧪 测试模型调用...")
        print(f"   - 使用的 base_url: {kwargs.get('base_url')}")
        print(f"   - 模型名称: {kwargs.get('model')}")

        try:
            response = model.invoke("Hello, please respond with just 'OK'")
            print(f"✅ 模型响应: {response.content}")
        except Exception as e:
            print(f"❌ 模型调用失败: {e}")
            print("💡 可能的解决方案:")
            print("   1. 检查中转服务是否正常运行")
            print("   2. 验证 API 密钥是否有效")
            print("   3. 确认中转服务支持 OpenAI 兼容接口")
            print("   4. 检查 base_url 是否正确")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ LangChain 模型测试失败: {e}")
        return False


if __name__ == "__main__":
    print("=" * 50)
    print("🔧 模型配置测试")
    print("=" * 50)
    
    # 测试基本配置
    config_ok = test_model_config()
    
    if config_ok:
        # 测试 LangChain 初始化
        langchain_ok = test_langchain_init()
        
        if langchain_ok:
            print("\n🎉 所有测试通过！模型配置正确。")
        else:
            print("\n⚠️ 模型配置正确，但 LangChain 初始化失败。")
    else:
        print("\n❌ 模型配置有问题，请检查环境变量。")
    
    print("=" * 50)
