"""
多服务器 MCP + <PERSON><PERSON><PERSON>n Agent 示例 (最终优化版)
---------------------------------------------
1. [异步] 读取 .env 和 servers_config.json
2. [异步] 启动并连接 MCP 服务器
3. [修复] 使用线程池处理阻塞调用，避免 "Blocking call" 错误
4. [优化] 使用全局缓存，实现重量级资源 (模型、工具) 的一次性加载，避免重复初始化，提升响应速度

🚨 运行提示：
   - 开发时: langgraph dev --allow-blocking
   - 或在 .env 文件中添加: BG_JOB_ISOLATED_LOOPS=true
"""
import asyncio
import json
import logging
import os
from typing import Any, Dict, List, Optional

import aiofiles
from dotenv import load_dotenv
from langgraph.prebuilt import create_react_agent
from langchain.chat_models import init_chat_model
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.memory import InMemorySaver
from langchain_tavily import TavilySearch
from model_config import get_model_config, create_model_kwargs, validate_model_environment, get_available_models

# 基础日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] - %(message)s'
)

# 在全局作用域加载环境变量
load_dotenv()
logging.info("✅ .env 文件加载完成 (如果存在)。")

# ✅ 创建Tavily搜索工具
# search_tool = TavilySearch(max_results=5, topic="general")

# <<< 新增: 全局变量，用于缓存昂贵且可复用的对象 >>>
_cached_tools: Optional[List[Any]] = None
_cached_model: Optional[Any] = None  # 支持任意类型的聊天模型
_cached_prompt: Optional[str] = None
# Checkpointer 也可以是单例的，在所有会话中共享
_checkpointer = InMemorySaver()


class Configuration:
    """
    [异步优化] 读取 .env 与 servers_config.json
    支持多种模型配置
    """
    def __init__(self) -> None:
        # 获取模型名称，默认为 deepseek-chat
        self.model: str = os.getenv("MODEL") or "deepseek-chat"

        # 验证模型是否支持
        if self.model not in get_available_models():
            available = ", ".join(get_available_models())
            raise ValueError(f"❌ 不支持的模型: {self.model}. 支持的模型: {available}")

        # 验证模型环境配置
        if not validate_model_environment(self.model):
            model_config = get_model_config(self.model)
            raise ValueError(f"❌ 未找到 {model_config.api_key_env} 环境变量，请在 .env 中配置")

        # 获取模型配置
        self.model_config = get_model_config(self.model)

        # 兼容旧的配置方式
        self.api_key: str = os.getenv(self.model_config.api_key_env) or ""
        self.base_url: str | None = self.model_config.base_url

    @staticmethod
    async def load_servers(file_path: str = "servers_config.json") -> Dict[str, Any]:
        """异步读取服务器配置文件"""
        try:
            async with aiofiles.open(file_path, "r", encoding="utf-8") as f:
                content = await f.read()
                return json.loads(content).get("mcpServers", {})
        except FileNotFoundError:
            logging.error(f"❌ 配置文件 {file_path} 未找到。")
            return {}
        except json.JSONDecodeError as e:
            logging.error(f"❌ 配置文件 {file_path} 格式错误: {e}")
            return {}


def _get_mcp_tools_sync(servers_cfg: Dict[str, Any]) -> List[Any]:
    """
    一个同步的包装函数，用于在独立的线程中执行所有可能阻塞的MCP操作。
    """
    logging.info("-[工作线程]- 开始初始化MCP客户端...")
    client = MultiServerMCPClient(servers_cfg)
    logging.info("-[工作线程]- 正在获取工具...")
    try:
        tools = asyncio.run(client.get_tools())
        logging.info(f"-[工作线程]- 成功加载 {len(tools)} 个 MCP 工具: {[t.name for t in tools]}")
        return tools
    except Exception as e:
        logging.error(f"-[工作线程]- 在获取工具时发生错误: {e}")
        return []


async def get_mcp_tools() -> List[Any]:
    """
    [健壮版本] 异步获取MCP工具，将所有阻塞操作隔离到单独的线程中。
    """
    try:
        servers_cfg = await Configuration.load_servers()
        if not servers_cfg:
            logging.warning("⚠️  没有找到有效的MCP服务器配置")
            return []
        logging.info("🔄 准备在独立线程中获取MCP工具以避免阻塞...")
        tools = await asyncio.to_thread(_get_mcp_tools_sync, servers_cfg)
        if tools:
            logging.info(f"✅ 主线程成功接收到 {len(tools)} 个工具。")
        else:
            logging.warning("⚠️  未能从工作线程获取到任何MCP工具。")
        return tools
    except Exception as e:
        logging.error(f"❌ 获取MCP工具的异步调度失败: {e}")
        return []


async def load_prompt() -> str:
    """异步加载提示词文件"""
    try:
        async with aiofiles.open("agent_prompts.txt", "r", encoding="utf-8") as f:
            return await f.read()
    except FileNotFoundError:
        logging.warning("⚠️  未找到 agent_prompts.txt 文件，使用默认提示词。")
        return """你是一个智能助手，可以使用各种工具来帮助用户完成任务。
请根据用户的需求选择合适的工具，并提供准确的回答。"""


# <<< 新增: 启动函数，只在程序启动时运行一次 >>>
async def startup_initialize():
    """
    在应用启动时执行一次，初始化所有昂贵的、可复用的资源，并将其存入全局变量。
    """
    global _cached_tools, _cached_model, _cached_prompt

    # 通过检查任一全局变量，防止重复初始化
    if _cached_model is not None:
        logging.info("✅ 全局资源已初始化，跳过启动流程。")
        return

    logging.info("🚀 [首次启动] 开始初始化全局资源...")

    # 1️⃣ 异步加载配置和提示词
    cfg = Configuration()
    # 使用 asyncio.gather 并行加载 prompt 和 tools，提升启动速度
    prompt_task = load_prompt()
    tools_task = get_mcp_tools()
    results = await asyncio.gather(prompt_task, tools_task)
    _cached_prompt, _cached_tools = results
    logging.info("✅ 提示词和工具加载完成。")

    # <<< 2. Tavily 工具 >>>
    try:
        if os.getenv("TAVILY_API_KEY"):
            logging.info("...检测到 TAVILY_API_KEY，正在添加 TavilySearch 工具...")
            search_tool = TavilySearch(max_results=5, topic="general")
            # 将新工具添加到已有的工具列表中
            _cached_tools.append(search_tool)
            logging.info(f"✅ TavilySearch 工具已成功添加！当前工具总数: {len(_cached_tools)}")
        else:
            logging.warning("⚠️  未找到 TAVILY_API_KEY 环境变量，跳过添加 TavilySearch 工具。")
    except Exception as e:
        logging.error(f"❌ 添加 TavilySearch 工具失败: {e}")

    # 2️⃣ 初始化大模型
    try:
        # 创建模型初始化参数
        model_kwargs = create_model_kwargs(
            cfg.model,
            timeout=30.0
        )

        # 使用 init_chat_model 初始化模型
        _cached_model = init_chat_model(**model_kwargs)
        logging.info(f"✅ {cfg.model} 模型已成功初始化并缓存")
        logging.info(f"   - 提供商: {cfg.model_config.provider}")
        logging.info(f"   - API地址: {cfg.model_config.base_url or '默认'}")
    except Exception as e:
        logging.error(f"❌ {cfg.model} 模型初始化失败: {e}")
        raise

    logging.info("🎉 [首次启动] 所有全局资源初始化完成！")


# <<< 修改: make_graph 函数现在变得非常轻量 >>>
async def make_graph():
    """
    [高效版] 创建Agent图。
    此函数会重复调用，但它现在只执行轻量级的 Agent 组装操作，
    所有昂贵的资源都从全局缓存中直接获取。
    """
    # 1️⃣ 确保所有资源已经被初始化
    #    这是安全保障，确保即使直接调用 make_graph 也能正常工作
    if _cached_model is None:
        await startup_initialize()

    logging.info("...使用已缓存的资源创建 Agent 实例...")

    # 2️⃣ 使用已缓存的全局对象创建Agent
    try:
        agent = create_react_agent(
            model=_cached_model,
            tools=_cached_tools,
            prompt=_cached_prompt,
            checkpointer=_checkpointer
        )
        logging.info(f"✅ LangGraph Agent 实例创建成功，使用模型: {_cached_model.__class__.__name__}")
        return agent
    except Exception as e:
        logging.error(f"❌ 创建Agent实例失败: {e}")
        raise

# 提示: 在你的应用入口（例如 FastAPI 的 @app.on_event("startup") 或主脚本），
# 你应该调用 asyncio.run(startup_initialize())，
# 以确保在接收任何请求之前，所有资源都已准备就绪。
# langgraph dev 会自动处理这个过程，它会在需要时调用 make_graph，
# 而我们已经在 make_graph 中加入了安全检查。