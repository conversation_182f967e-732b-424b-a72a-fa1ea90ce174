#!/usr/bin/env python3
"""
测试不同的 Gemini 配置
"""
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_openai_compatible():
    """测试 OpenAI 兼容接口"""
    try:
        from langchain.chat_models import init_chat_model
        
        print("🔧 测试 OpenAI 兼容接口...")
        
        model = init_chat_model(
            model="gemini-2.5-pro",
            model_provider="openai",
            api_key=os.getenv("GOOGLE_API_KEY"),
            base_url=os.getenv("GEMINI_BASE_URL"),
            temperature=0.7
        )
        
        print(f"✅ 模型初始化成功: {type(model).__name__}")
        
        # 测试调用
        response = model.invoke("Hello")
        print(f"✅ 响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ OpenAI 兼容接口失败: {e}")
        return False


def test_google_genai():
    """测试 Google GenAI 接口"""
    try:
        from langchain.chat_models import init_chat_model
        
        print("\n🔧 测试 Google GenAI 接口...")
        
        # 尝试不使用 base_url
        model = init_chat_model(
            model="gemini-2.5-pro",
            model_provider="google_genai",
            api_key=os.getenv("GOOGLE_API_KEY"),
            temperature=0.7
        )
        
        print(f"✅ 模型初始化成功: {type(model).__name__}")
        
        # 测试调用
        response = model.invoke("Hello")
        print(f"✅ 响应: {response.content}")
        return True
        
    except Exception as e:
        print(f"❌ Google GenAI 接口失败: {e}")
        return False


def test_different_models():
    """测试不同的模型名称"""
    models_to_test = [
        "gemini-1.5-pro",
        "gemini-1.5-flash", 
        "gemini-pro",
        "gemini-pro-latest"
    ]
    
    print("\n🔧 测试不同的模型名称...")
    
    for model_name in models_to_test:
        try:
            from langchain.chat_models import init_chat_model
            
            print(f"\n   测试模型: {model_name}")
            
            # 使用 OpenAI 兼容接口
            model = init_chat_model(
                model=model_name,
                model_provider="openai",
                api_key=os.getenv("GOOGLE_API_KEY"),
                base_url=os.getenv("GEMINI_BASE_URL"),
                temperature=0.7
            )
            
            response = model.invoke("Hi")
            print(f"   ✅ {model_name}: {response.content[:50]}...")
            return True
            
        except Exception as e:
            print(f"   ❌ {model_name}: {e}")
            continue
    
    return False


def test_url_variations():
    """测试不同的 URL 配置"""
    base_url = os.getenv("GEMINI_BASE_URL", "")
    
    url_variations = [
        base_url,
        base_url + "/v1",
        base_url + "/v1/chat/completions",
        base_url.replace("https://", "http://") if base_url.startswith("https://") else base_url,
    ]
    
    print(f"\n🔧 测试不同的 URL 配置...")
    print(f"   原始 URL: {base_url}")
    
    for url in url_variations:
        if not url:
            continue
            
        try:
            from langchain.chat_models import init_chat_model
            
            print(f"\n   测试 URL: {url}")
            
            model = init_chat_model(
                model="gemini-2.5-pro",
                model_provider="openai",
                api_key=os.getenv("GOOGLE_API_KEY"),
                base_url=url,
                temperature=0.7
            )
            
            response = model.invoke("Test")
            print(f"   ✅ 成功: {response.content[:30]}...")
            return True
            
        except Exception as e:
            print(f"   ❌ 失败: {str(e)[:100]}...")
            continue
    
    return False


if __name__ == "__main__":
    print("=" * 60)
    print("🧪 Gemini 配置测试")
    print("=" * 60)
    
    print(f"🔑 API Key: {os.getenv('GOOGLE_API_KEY', 'Not Set')}")
    print(f"🌐 Base URL: {os.getenv('GEMINI_BASE_URL', 'Not Set')}")
    
    # 测试不同的配置方法
    tests = [
        ("OpenAI 兼容接口", test_openai_compatible),
        ("Google GenAI 接口", test_google_genai),
        ("不同模型名称", test_different_models),
        ("不同 URL 配置", test_url_variations),
    ]
    
    success = False
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"🎉 {test_name} 测试成功！")
                success = True
                break
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    if not success:
        print("\n" + "="*60)
        print("❌ 所有测试都失败了")
        print("💡 建议:")
        print("1. 检查中转服务是否正常运行")
        print("2. 验证 API 密钥格式是否正确")
        print("3. 确认中转服务的 API 接口规范")
        print("4. 尝试使用 curl 直接测试中转服务")
    
    print("=" * 60)
